model customerPhoneDestination {
  customerId           String   @map(name: "customer_id") @db.Uuid
  phoneNumber          String   @map(name: "phone_number")
  communicationChannel String   @default("WHATSAPPSELFHOSTED") @map(name: "communication_channel")
  destination          String
  status               String   @default("ACTIVE")
  createdAt            DateTime @default(now()) @map(name: "created_at")
  updatedAt            DateTime @updatedAt @map(name: "updated_at")

  @@id([customerId, phoneNumber, destination], name: "customer_phone_destination_pkey")
  @@unique([customerId, destination, communicationChannel], name: "customer_phone_destination_customer_id_destination_communication_channel_unique")
  @@index([customerId], name: "customer_phone_destination_customer_id_index")
  @@map(name: "customer_phones_destination")
  @@schema("message_hub")
}
