import { CustomerPreferencesDto } from '@business-base/application/dto/customer-preferences.dto';
import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { getAuthCredentials } from 'test/helpers/authenticated-user';

describe('CustomerPreferences (e2e)', () => {
  let app: INestApplication;
  let accessToken: string;

  beforeAll(async () => {
    app = global.__NEST_APP__;

    const { accessToken: token } = await getAuthCredentials(
      app,
      '<EMAIL>',
      'password123',
    );
    accessToken = token;
  });

  describe('POST /v1/business-base/customer-preferences/:customerId', () => {
    it('should create customer preferences successfully', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      const response = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(201);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customerId).toEqual(customerId);
      expect(response.body.data.portfolio).toBeDefined();
      expect(response.body.data.portfolio.defaultWorkflowId).toEqual(
        customerPreferences.portfolio.defaultWorkflowId,
      );
      expect(response.body.data.portfolio.timezoneUTC).toEqual(
        customerPreferences.portfolio.timezoneUTC,
      );
      expect(response.body.data.portfolio.importCronExpression).toEqual(
        customerPreferences.portfolio.importCronExpression,
      );
      expect(response.body.data.portfolio.followUpWorkflowId).toEqual(
        customerPreferences.portfolio.followUpWorkflowId,
      );
      expect(response.body.data.portfolio.followUpCronExpression).toEqual(
        customerPreferences.portfolio.followUpCronExpression,
      );
      expect(response.body.data.portfolio.followUpQuantity).toEqual(
        customerPreferences.portfolio.followUpQuantity,
      );
      expect(response.body.data.portfolio.followUpIntervalMinutes).toEqual(
        customerPreferences.portfolio.followUpIntervalMinutes,
      );
      expect(response.body.data.portfolio.exportColumns).toEqual(
        customerPreferences.portfolio.exportColumns,
      );
    });

    it('should fail to create duplicate customer preferences', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create first preferences
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Attempt to create duplicate
      const duplicateResponse = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(500); // TODO: Should be 400, but BusinessException mapping needs to be fixed

      expect(duplicateResponse.body).toBeDefined();
      expect(duplicateResponse.body.statusCode).toEqual(500);
      expect(duplicateResponse.body.message).toBeDefined();
      expect(
        Array.isArray(duplicateResponse.body.message)
          ? duplicateResponse.body.message[0]
          : duplicateResponse.body.message,
      ).toContain('Customer preferences already exist');
    });

    it('should fail with invalid data validation', async () => {
      const customerId = uuidv4();
      const invalidPreferences = {
        portfolio: {
          defaultWorkflowId: 'invalid-uuid',
          timezoneUTC: 'invalid-timezone',
          importCronExpression: 'invalid-cron',
          followUpWorkflowId: 'invalid-uuid',
          followUpCronExpression: 'invalid-cron',
          followUpQuantity: -1,
          followUpIntervalMinutes: 0,
          exportColumns: ['valid-column'],
        },
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(invalidPreferences)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toBeDefined();
      expect(Array.isArray(response.body.message)).toBe(true);
      expect(response.body.message.length).toBeGreaterThan(0);
    });

    it('should reject unknown properties at root level - strict DTO validation', async () => {
      const customerId = uuidv4();
      const preferencesWithUnknownProps = {
        portfolio: {
          defaultWorkflowId: '6f413811-4aa8-43f4-8c48-d00143dd226d',
          timezoneUTC: '-3',
        },
        unknownProperty: 'should-be-rejected',
        anotherUnknownProp: { nested: 'value' },
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(preferencesWithUnknownProps)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toBeDefined();
      expect(Array.isArray(response.body.message)).toBe(true);
      expect(response.body.message.some(msg => msg.includes('unknownProperty'))).toBe(true);
      expect(response.body.message.some(msg => msg.includes('anotherUnknownProp'))).toBe(true);
    });

    it('should reject unknown properties in nested portfolio object - strict DTO validation', async () => {
      const customerId = uuidv4();
      const preferencesWithNestedUnknownProps = {
        portfolio: {
          defaultWorkflowId: '6f413811-4aa8-43f4-8c48-d00143dd226d',
          timezoneUTC: '-3',
          unknownNestedProp: 'should-be-rejected',
          anotherNestedProp: 123,
        },
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(preferencesWithNestedUnknownProps)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toBeDefined();
      expect(Array.isArray(response.body.message)).toBe(true);
      expect(response.body.message.some(msg => msg.includes('unknownNestedProp'))).toBe(true);
      expect(response.body.message.some(msg => msg.includes('anotherNestedProp'))).toBe(true);
    });

    it('should reject the exact invalid data from the example - prevents data corruption', async () => {
      const customerId = uuidv4();
      const invalidDataFromExample = {
        portfolio: {
          exportColumns: [],
          followUpWorkflowId: '7f413811-4aa8-43f4-8c48-d00143dd226e',
          prop: 'value', // ❌ Not defined in DTO - should be rejected
        },
        teste: {}, // ❌ Not defined in DTO - should be rejected
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(invalidDataFromExample)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toBeDefined();
      expect(Array.isArray(response.body.message)).toBe(true);
      expect(response.body.message.some(msg => msg.includes('teste'))).toBe(true);
      expect(response.body.message.some(msg => msg.includes('prop'))).toBe(true);
    });

    it('should create customer preferences with partial data', async () => {
      const customerId = uuidv4();
      const partialPreferences = {
        portfolio: {
          defaultWorkflowId: '6f413811-4aa8-43f4-8c48-d00143dd226d',
          timezoneUTC: '-3',
          // Only providing some fields - all others are optional
        },
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(partialPreferences)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(201);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customerId).toEqual(customerId);
      expect(response.body.data.portfolio.defaultWorkflowId).toEqual(
        partialPreferences.portfolio.defaultWorkflowId,
      );
      expect(response.body.data.portfolio.timezoneUTC).toEqual(
        partialPreferences.portfolio.timezoneUTC,
      );
      // Optional fields should be undefined
      expect(response.body.data.portfolio.importCronExpression).toBeUndefined();
      expect(response.body.data.portfolio.followUpWorkflowId).toBeUndefined();
    });

    it('should create customer preferences with empty portfolio', async () => {
      const customerId = uuidv4();
      const emptyPreferences = {
        portfolio: {},
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(emptyPreferences)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(201);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customerId).toEqual(customerId);
      expect(response.body.data.portfolio).toBeDefined();
      // All portfolio fields should be undefined
      expect(response.body.data.portfolio.defaultWorkflowId).toBeUndefined();
      expect(response.body.data.portfolio.timezoneUTC).toBeUndefined();
    });

    it('should create customer preferences without portfolio', async () => {
      const customerId = uuidv4();
      const noPortfolioPreferences = {};

      const response = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(noPortfolioPreferences)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(201);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customerId).toEqual(customerId);
      // Portfolio should be undefined when not provided
      expect(response.body.data.portfolio).toBeUndefined();
    });

    it('should demonstrate semi-dynamic architecture with defined properties only', async () => {
      const customerId = uuidv4();
      const preferencesWithDefinedProperties = {
        portfolio: {
          defaultWorkflowId: '6f413811-4aa8-43f4-8c48-d00143dd226d',
          timezoneUTC: '-3',
          followUpQuantity: 5,
          followUpIntervalMinutes: 1440,
          exportColumns: ['name', 'phone', 'status'],
        },
      };

      const response = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(preferencesWithDefinedProperties)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(201);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customerId).toEqual(customerId);
      expect(response.body.data.portfolio.defaultWorkflowId).toEqual(
        preferencesWithDefinedProperties.portfolio.defaultWorkflowId,
      );
      expect(response.body.data.portfolio.timezoneUTC).toEqual(
        preferencesWithDefinedProperties.portfolio.timezoneUTC,
      );
      expect(response.body.data.portfolio.followUpQuantity).toEqual(
        preferencesWithDefinedProperties.portfolio.followUpQuantity,
      );
    });
  });

  describe('GET /v1/business-base/customer-preferences/:customerId', () => {
    it('should retrieve customer preferences successfully', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences first
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Retrieve preferences
      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customerId).toEqual(customerId);
      expect(response.body.data.portfolio).toBeDefined();
      expect(response.body.data.portfolio.defaultWorkflowId).toEqual(
        customerPreferences.portfolio.defaultWorkflowId,
      );
      expect(response.body.data.portfolio.timezoneUTC).toEqual(
        customerPreferences.portfolio.timezoneUTC,
      );
    });

    it('should return 404 for non-existent customer preferences', async () => {
      const nonExistentCustomerId = uuidv4();

      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${nonExistentCustomerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(404);
      expect(response.body.message).toBeDefined();
      expect(response.body.message[0]).toContain('Customer preferences not found');
    });

    it('should return 404 for deleted customer preferences', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Delete preferences (soft delete)
      await request(app.getHttpServer())
        .delete(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      // Try to retrieve deleted preferences
      const response = await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(404);
      expect(response.body.message[0]).toContain('Customer preferences not found');
    });
  });

  describe('PUT /v1/business-base/customer-preferences/:customerId', () => {
    it('should update customer preferences successfully', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences first
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Update preferences
      const updateData = createValidUpdateCustomerPreferences();
      const response = await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateData)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customerId).toEqual(customerId);
      expect(response.body.data.portfolio.timezoneUTC).toEqual(updateData.portfolio.timezoneUTC);
      expect(response.body.data.portfolio.followUpQuantity).toEqual(
        updateData.portfolio.followUpQuantity,
      );
      expect(response.body.data.portfolio.exportColumns).toEqual(
        updateData.portfolio.exportColumns,
      );
    });

    it('should perform full replacement with PUT semantics', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences first
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Full replacement - only timezone provided, other fields should be undefined
      const replacementUpdate = {
        portfolio: {
          timezoneUTC: '+2',
        },
      };

      const response = await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(replacementUpdate)
        .expect(200);

      expect(response.body.data.portfolio.timezoneUTC).toEqual('+2');
      // Other fields should be undefined due to PUT semantics (full replacement)
      expect(response.body.data.portfolio.defaultWorkflowId).toBeUndefined();
      expect(response.body.data.portfolio.followUpQuantity).toBeUndefined();
      expect(response.body.data.portfolio.importCronExpression).toBeUndefined();
    });

    it('should update with empty portfolio data', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences first
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Update with empty portfolio - should clear all portfolio data
      const emptyUpdate = {
        portfolio: {},
      };

      const response = await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(emptyUpdate)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.customerId).toEqual(customerId);
      expect(response.body.data.portfolio).toBeDefined();
      // All portfolio fields should be undefined after empty update
      expect(response.body.data.portfolio.defaultWorkflowId).toBeUndefined();
      expect(response.body.data.portfolio.timezoneUTC).toBeUndefined();
      expect(response.body.data.portfolio.importCronExpression).toBeUndefined();
    });

    it('should return 404 for non-existent customer preferences', async () => {
      const nonExistentCustomerId = uuidv4();
      const updateData = createValidUpdateCustomerPreferences();

      const response = await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${nonExistentCustomerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateData)
        .expect(404);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(404);
      expect(response.body.message[0]).toContain('Customer preferences not found');
    });

    it('should fail with invalid data validation', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences first
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Invalid update data
      const invalidUpdate = {
        portfolio: {
          timezoneUTC: 'invalid-timezone',
          followUpQuantity: -5,
          importCronExpression: 'invalid-cron',
        },
      };

      const response = await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(invalidUpdate)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(Array.isArray(response.body.message)).toBe(true);
    });

    it('should reject unknown properties in PUT requests - strict DTO validation', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences first
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Update with unknown properties
      const updateWithUnknownProps = {
        portfolio: {
          timezoneUTC: '-2',
          unknownNestedProp: 'should-be-rejected',
        },
        unknownRootProp: 'should-be-rejected',
      };

      const response = await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateWithUnknownProps)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toBeDefined();
      expect(Array.isArray(response.body.message)).toBe(true);
      expect(response.body.message.some(msg => msg.includes('unknownNestedProp'))).toBe(true);
      expect(response.body.message.some(msg => msg.includes('unknownRootProp'))).toBe(true);
    });

    it('should demonstrate semi-dynamic architecture with PUT requests using defined properties', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences first
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Update with defined properties - demonstrates semi-dynamic handling
      const updateWithDefinedProperties = {
        portfolio: {
          timezoneUTC: '-2',
          followUpQuantity: 3,
          followUpIntervalMinutes: 2880,
          exportColumns: ['name', 'email', 'status', 'created_at'],
        },
      };

      const response = await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateWithDefinedProperties)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.portfolio.timezoneUTC).toEqual('-2');
      expect(response.body.data.portfolio.followUpQuantity).toEqual(3);
      expect(response.body.data.portfolio.followUpIntervalMinutes).toEqual(2880);
      expect(response.body.data.portfolio.exportColumns).toEqual(['name', 'email', 'status', 'created_at']);
    });
  });

  describe('DELETE /v1/business-base/customer-preferences/:customerId', () => {
    it('should soft delete customer preferences successfully', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences first
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Verify preferences exist
      await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      // Delete preferences
      const response = await request(app.getHttpServer())
        .delete(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(200);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.message).toEqual('Customer preferences deleted successfully');

      // Verify preferences are no longer retrievable (soft delete behavior)
      await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);
    });

    it('should return 404 when attempting to delete non-existent customer preferences', async () => {
      const nonExistentCustomerId = uuidv4();

      const response = await request(app.getHttpServer())
        .delete(`/api/v1/business-base/customer-preferences/${nonExistentCustomerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(404);
      expect(response.body.message[0]).toContain('Customer preferences not found');
    });

    it('should return 404 when attempting to delete already deleted customer preferences', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Delete preferences first time
      await request(app.getHttpServer())
        .delete(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      // Attempt to delete again
      const response = await request(app.getHttpServer())
        .delete(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(404);
      expect(response.body.message[0]).toContain('Customer preferences not found');
    });

    it('should verify soft delete behavior - record still exists in database with DELETED status', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // Create preferences
      await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      // Delete preferences (soft delete)
      await request(app.getHttpServer())
        .delete(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      // Verify the record appears deleted to the API
      await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);

      // Note: In a real test environment, you would verify the record still exists in DynamoDB
      // with status='DELETED', but this requires direct database access which is not available
      // in this e2e test context. The soft delete behavior is verified by the fact that:
      // 1. The delete operation succeeds
      // 2. Subsequent GET requests return 404
      // 3. Subsequent DELETE requests return 404 (record is filtered out)
    });
  });

  describe('Edge Cases and Integration Tests', () => {
    it('should handle complete CRUD lifecycle correctly', async () => {
      const customerId = uuidv4();
      const customerPreferences = createValidCustomerPreferences();

      // 1. Create
      const createResponse = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(customerPreferences)
        .expect(201);

      expect(createResponse.body.data.customerId).toEqual(customerId);

      // 2. Read
      const readResponse = await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(readResponse.body.data.customerId).toEqual(customerId);

      // 3. Update
      const updateData = { portfolio: { timezoneUTC: '+5' } };
      const updateResponse = await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateData)
        .expect(200);

      expect(updateResponse.body.data.portfolio.timezoneUTC).toEqual('+5');

      // 4. Delete
      await request(app.getHttpServer())
        .delete(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      // 5. Verify deletion
      await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(404);
    });

    it('should handle various timezone formats correctly', async () => {
      const testCases = [
        { timezone: '-3', description: 'negative integer' },
        { timezone: '+5', description: 'positive integer' },
        { timezone: '-5.5', description: 'negative decimal' },
        { timezone: '+9.5', description: 'positive decimal' },
        { timezone: '0', description: 'zero' },
      ];

      for (const testCase of testCases) {
        const customerId = uuidv4();
        const customerPreferences = {
          portfolio: {
            defaultWorkflowId: '6f413811-4aa8-43f4-8c48-d00143dd226d',
            timezoneUTC: testCase.timezone,
            importCronExpression: '0 9 * * 1-5',
            followUpWorkflowId: '7f413811-4aa8-43f4-8c48-d00143dd226e',
            followUpCronExpression: '0 */2 * * *',
            followUpQuantity: 3,
            followUpIntervalMinutes: 120,
            exportColumns: ['name', 'phone', 'status'],
          },
        };

        const response = await request(app.getHttpServer())
          .post(`/api/v1/business-base/customer-preferences/${customerId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(customerPreferences)
          .expect(201);

        expect(response.body.data.portfolio.timezoneUTC).toEqual(testCase.timezone);
      }
    });

    it('should handle various cron expression formats correctly', async () => {
      const testCases = [
        { cron: '0 9 * * 1-5', description: 'weekdays at 9 AM' },
        { cron: '*/15 * * * *', description: 'every 15 minutes' },
        { cron: '0 */2 * * *', description: 'every 2 hours' },
        { cron: '0 0 1 * *', description: 'first day of month' },
        { cron: '0 0 * * 0', description: 'every Sunday' },
      ];

      for (const testCase of testCases) {
        const customerId = uuidv4();
        const customerPreferences = {
          portfolio: {
            defaultWorkflowId: '6f413811-4aa8-43f4-8c48-d00143dd226d',
            timezoneUTC: '-3',
            importCronExpression: testCase.cron,
            followUpWorkflowId: '7f413811-4aa8-43f4-8c48-d00143dd226e',
            followUpCronExpression: testCase.cron,
            followUpQuantity: 3,
            followUpIntervalMinutes: 120,
            exportColumns: ['name', 'phone', 'status'],
          },
        };

        const response = await request(app.getHttpServer())
          .post(`/api/v1/business-base/customer-preferences/${customerId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(customerPreferences)
          .expect(201);

        expect(response.body.data.portfolio.importCronExpression).toEqual(testCase.cron);
        expect(response.body.data.portfolio.followUpCronExpression).toEqual(testCase.cron);
      }
    });

    it('should handle empty and various export columns arrays', async () => {
      const testCases = [
        { columns: [], description: 'empty array' },
        { columns: ['name'], description: 'single column' },
        { columns: ['name', 'phone', 'status'], description: 'multiple columns' },
        {
          columns: [
            'name',
            'phone',
            'status',
            'lastInteraction',
            'followUpCount',
            'createdAt',
            'updatedAt',
          ],
          description: 'many columns',
        },
      ];

      for (const testCase of testCases) {
        const customerId = uuidv4();
        const customerPreferences = {
          portfolio: {
            defaultWorkflowId: '6f413811-4aa8-43f4-8c48-d00143dd226d',
            timezoneUTC: '-3',
            importCronExpression: '0 9 * * 1-5',
            followUpWorkflowId: '7f413811-4aa8-43f4-8c48-d00143dd226e',
            followUpCronExpression: '0 */2 * * *',
            followUpQuantity: 3,
            followUpIntervalMinutes: 120,
            exportColumns: testCase.columns,
          },
        };

        const response = await request(app.getHttpServer())
          .post(`/api/v1/business-base/customer-preferences/${customerId}`)
          .set('Authorization', `Bearer ${accessToken}`)
          .send(customerPreferences)
          .expect(201);

        expect(response.body.data.portfolio.exportColumns).toEqual(testCase.columns);
      }
    });

    it('should demonstrate semi-dynamic architecture with strict DTO validation', async () => {
      // This test demonstrates the semi-dynamic architecture with strict validation:
      // 1. API Layer: Only properties explicitly defined in DTOs are accepted (strict validation)
      // 2. Service Layer: Automatically handles any properties defined in DTOs (no code changes needed)
      // 3. Repository Layer: Automatically persists any properties from entities (no code changes needed)
      //
      // To add new properties:
      // - Developer must explicitly define them in DTOs and entities with proper validation
      // - Service and repository layers automatically handle them through dynamic object operations
      // - This prevents data corruption while maintaining flexibility for new features

      const customerId = uuidv4();
      const createData = createValidCustomerPreferences();

      // Create initial preferences with only defined properties
      const createResponse = await request(app.getHttpServer())
        .post(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(createData)
        .expect(201);

      expect(createResponse.body.data.customerId).toEqual(customerId);
      expect(createResponse.body.data.portfolio).toBeDefined();

      // Attempt to update with undefined properties - should be rejected
      const updateDataWithUnknownProps = {
        portfolio: {
          timezoneUTC: '-2',
          unknownProperty: 'should-be-rejected',
        },
        anotherUnknownProp: 'should-also-be-rejected',
      };

      await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(updateDataWithUnknownProps)
        .expect(400); // Should fail due to strict validation

      // Update with only defined properties - should succeed and demonstrate semi-dynamic handling
      const validUpdateData = {
        portfolio: {
          timezoneUTC: '-2',
          followUpQuantity: 2,
          followUpIntervalMinutes: 1440,
          exportColumns: ['name', 'phone', 'email'],
        },
      };

      const updateResponse = await request(app.getHttpServer())
        .put(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .send(validUpdateData)
        .expect(200);

      expect(updateResponse.body.data.portfolio.timezoneUTC).toBe('-2');
      expect(updateResponse.body.data.portfolio.followUpQuantity).toBe(2);
      expect(updateResponse.body.data.portfolio.followUpIntervalMinutes).toBe(1440);
      expect(updateResponse.body.data.portfolio.exportColumns).toEqual(['name', 'phone', 'email']);

      // Verify retrieval works correctly
      const getResponse = await request(app.getHttpServer())
        .get(`/api/v1/business-base/customer-preferences/${customerId}`)
        .set('Authorization', `Bearer ${accessToken}`)
        .expect(200);

      expect(getResponse.body.data.portfolio.timezoneUTC).toBe('-2');
      expect(getResponse.body.data.portfolio.followUpQuantity).toBe(2);
      expect(getResponse.body.data.portfolio.followUpIntervalMinutes).toBe(1440);
      expect(getResponse.body.data.portfolio.exportColumns).toEqual(['name', 'phone', 'email']);
    });
  });

  // Helper functions
  function createValidCustomerPreferences(): CustomerPreferencesDto {
    return new CustomerPreferencesDto({
      portfolio: {
        defaultWorkflowId: '6f413811-4aa8-43f4-8c48-d00143dd226d',
        timezoneUTC: '-3',
        importCronExpression: '0 9 * * 1-5',
        followUpWorkflowId: '7f413811-4aa8-43f4-8c48-d00143dd226e',
        followUpCronExpression: '0 */2 * * *',
        followUpQuantity: 3,
        followUpIntervalMinutes: 120,
        exportColumns: ['name', 'phone', 'status', 'lastInteraction', 'followUpCount'],
      },
    });
  }

  function createValidUpdateCustomerPreferences(): any {
    return {
      portfolio: {
        timezoneUTC: '-5',
        followUpQuantity: 5,
        exportColumns: ['name', 'phone', 'status', 'lastInteraction', 'followUpCount', 'createdAt'],
      },
    };
  }


});
