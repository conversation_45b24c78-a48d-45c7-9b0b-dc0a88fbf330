import { INestApplication } from '@nestjs/common';
import request from 'supertest';
import { v4 as uuidv4 } from 'uuid';
import { IndexDocumentDto } from '@data-insights/application/dto/in/index-document.dto';
import { SearchSimilarDto } from '@data-insights/application/dto/in/search-similar.dto';

describe('SearchController (e2e)', () => {
  let app: INestApplication;
  const baseUrl = '/api/v1/data-insights/search';

  beforeAll(async () => {
    app = global.__NEST_APP__;
  });

  describe('/v1/data-insights/search/index (POST)', () => {
    it('should index a document successfully', async () => {
      const indexDocumentDto: IndexDocumentDto = {
        index: 'test-index',
        document: {
          title: 'Test Document',
          content: 'This is a test document for indexing',
          category: 'test',
          customerId: uuidv4(),
          tags: ['test', 'document', 'search'],
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}/index`)
        .send(indexDocumentDto)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(201);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBeDefined();
      expect(response.body.data.result).toBe('created');
    });

    it('should index a complex document with nested objects', async () => {
      const indexDocumentDto: IndexDocumentDto = {
        index: 'complex-index',
        document: {
          customerId: uuidv4(),
          title: 'Complex Document',
          metadata: {
            author: 'John Doe',
            created: new Date().toISOString(),
            version: '1.0',
          },
          sections: [
            {
              title: 'Introduction',
              content: 'This is the introduction section',
            },
            {
              title: 'Main Content',
              content: 'This is the main content section with detailed information',
            },
          ],
          tags: ['complex', 'nested', 'document'],
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}/index`)
        .send(indexDocumentDto)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(201);
      expect(response.body.data).toBeDefined();
      expect(response.body.data.id).toBeDefined();
    });

    it('should return 400 when index is missing', async () => {
      const invalidDto = {
        customerId: uuidv4(),
        document: {
          title: 'Test Document',
          content: 'This is a test document',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}/index`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('index is required');
    });

    it('should return 400 when document is missing', async () => {
      const invalidDto = {
        customerId: uuidv4(),
        index: 'test-index',
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}/index`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('document is required');
    });

    it('should return 400 when index is empty string', async () => {
      const invalidDto = {
        customerId: uuidv4(),
        index: '',
        document: {
          title: 'Test Document',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}/index`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('index is required');
    });

    it('should return 400 when document is not an object', async () => {
      const invalidDto = {
        customerId: uuidv4(),
        index: 'test-index',
        document: 'not an object',
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}/index`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toBeDefined();
    });
  });

  describe('/v1/data-insights/search (POST)', () => {
    beforeEach(async () => {
      // Index some test documents for similarity search
      const testDocuments = [
        {
          customerId: 'test-customer-1',
          index: 'similarity-test',
          document: {
            title: 'Machine Learning Basics',
            content: 'Introduction to machine learning algorithms and concepts',
            category: 'technology',
            tags: ['ml', 'ai', 'algorithms'],
          },
        },
        {
          customerId: 'test-customer-1',
          index: 'similarity-test',
          document: {
            title: 'Deep Learning Neural Networks',
            content: 'Advanced neural network architectures for deep learning',
            category: 'technology',
            tags: ['deep-learning', 'neural-networks', 'ai'],
          },
        },
        {
          customerId: 'test-customer-1',
          index: 'similarity-test',
          document: {
            title: 'Cooking Recipes',
            content: 'Delicious recipes for home cooking and baking',
            category: 'food',
            tags: ['cooking', 'recipes', 'food'],
          },
        },
      ];

      // Index all test documents
      for (const doc of testDocuments) {
        await request(app.getHttpServer()).post(`${baseUrl}/index`).send(doc);
      }

      // Wait a bit for indexing to complete
      await new Promise(resolve => setTimeout(resolve, 1000));
    });

    it('should find similar documents successfully', async () => {
      const searchSimilarDto: SearchSimilarDto = {
        index: 'similarity-test',
        size: 5,
        like: 'artificial intelligence and machine learning',
        fields: ['title', 'content'],
        filters: {
          category: 'technology',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(searchSimilarDto)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(201);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);

      // Should return documents with similarity scores
      if (response.body.data.length > 0) {
        expect(response.body.data[0]).toHaveProperty('score');
        expect(response.body.data[0]).toHaveProperty('category');
      }
    });

    it('should filter results by category', async () => {
      const searchSimilarDto: SearchSimilarDto = {
        index: 'similarity-test',
        size: 5,
        like: 'technology and algorithms',
        fields: ['title', 'content'],
        filters: {
          customerId: 'test-customer-1',
          category: 'technology',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(searchSimilarDto)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);

      // All returned documents should have category 'technology'
      response.body.data.forEach(doc => {
        expect(doc.category).toBe('technology');
      });
    });

    it('should limit results by size parameter', async () => {
      const searchSimilarDto: SearchSimilarDto = {
        index: 'similarity-test',
        size: 1,
        like: 'learning and education',
        fields: ['title', 'content'],
        filters: {
          category: 'technology',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(searchSimilarDto)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBeLessThanOrEqual(1);
    });

    it('should return empty array when no similar documents found', async () => {
      const searchSimilarDto: SearchSimilarDto = {
        index: 'similarity-test',
        size: 5,
        like: 'quantum physics and astronomy',
        fields: ['title', 'content'],
        filters: {
          category: 'nonexistent',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(searchSimilarDto)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should return 400 when index is missing', async () => {
      const invalidDto = {
        size: 5,
        like: 'test query',
        fields: ['title'],
        filters: {},
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('index is required');
    });

    it('should return 400 when size is missing', async () => {
      const invalidDto = {
        index: 'test-index',
        like: 'test query',
        fields: ['title'],
        filters: {},
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('size is required');
    });

    it('should return 400 when like is missing', async () => {
      const invalidDto = {
        index: 'test-index',
        size: 5,
        fields: ['title'],
        filters: {},
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('like is required');
    });

    it('should return 400 when fields is missing', async () => {
      const invalidDto = {
        index: 'test-index',
        size: 5,
        like: 'test query',
        filters: {},
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('fields is required');
    });

    it('should return 400 when filters is missing', async () => {
      const invalidDto = {
        index: 'test-index',
        size: 5,
        like: 'test query',
        fields: ['title'],
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('filters is required');
    });

    it('should return 400 when size is not a number', async () => {
      const invalidDto = {
        index: 'test-index',
        size: 'not-a-number',
        like: 'test query',
        fields: ['title'],
        filters: {},
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toBeDefined();
    });

    it('should return 400 when fields is not an array', async () => {
      const invalidDto = {
        index: 'test-index',
        size: 5,
        like: 'test query',
        fields: 'not-an-array',
        filters: {},
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toBeDefined();
    });

    it('should return 400 when filters is not an object', async () => {
      const invalidDto = {
        index: 'test-index',
        size: 5,
        like: 'test query',
        fields: ['title'],
        filters: 'not-an-object',
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(invalidDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toBeDefined();
    });

    it('should handle empty fields array', async () => {
      const searchSimilarDto: SearchSimilarDto = {
        index: 'similarity-test',
        size: 5,
        like: 'test query',
        fields: [],
        filters: {
          customerId: 'test-customer-1',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(searchSimilarDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('fields is required');
    });

    it('should handle empty filters object', async () => {
      const searchSimilarDto: SearchSimilarDto = {
        index: 'similarity-test',
        size: 5,
        like: 'test query',
        fields: ['title', 'content'],
        filters: {},
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(searchSimilarDto)
        .expect(400);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(400);
      expect(response.body.message).toContain('filters is required');
    });

    it('should search with multiple fields', async () => {
      const searchSimilarDto: SearchSimilarDto = {
        index: 'similarity-test',
        size: 5,
        like: 'machine learning algorithms',
        fields: ['title', 'content', 'tags'],
        filters: {
          category: 'technology',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(searchSimilarDto)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.statusCode).toEqual(201);
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
    });

    it('should handle non-existent index', async () => {
      const searchSimilarDto: SearchSimilarDto = {
        index: 'non-existent-index',
        size: 5,
        like: 'test query',
        fields: ['title'],
        filters: {
          category: 'test',
        },
      };

      const response = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(searchSimilarDto)
        .expect(201);

      expect(response.body).toBeDefined();
      expect(response.body.message).toBeDefined();
      expect(response.body.data).toBeDefined();
      expect(Array.isArray(response.body.data)).toBe(true);
      expect(response.body.data.length).toBe(0);
      expect(response.body.message).toBe('Index non-existent-index does not exist');
    });
  });

  describe('Integration tests', () => {
    it('should index a document and then find it with similarity search', async () => {
      const customerId = uuidv4();
      const index = 'integration-test';

      // First, index a document
      const indexDocumentDto: IndexDocumentDto = {
        index: index,
        document: {
          customerId,
          title: 'Integration Test Document',
          content: 'This document is used for integration testing of search functionality',
          category: 'test',
          tags: ['integration', 'test', 'search'],
        },
      };

      const indexResponse = await request(app.getHttpServer())
        .post(`${baseUrl}/index`)
        .send(indexDocumentDto)
        .expect(201);

      expect(indexResponse.body.data.id).toBeDefined();

      // Wait for indexing to complete
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Then, search for similar documents
      const searchSimilarDto: SearchSimilarDto = {
        index,
        size: 5,
        like: 'integration testing search',
        fields: ['title', 'content'],
        filters: {
          category: 'test',
        },
      };

      const searchResponse = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send(searchSimilarDto)
        .expect(201);

      expect(searchResponse.body.data).toBeDefined();
      expect(Array.isArray(searchResponse.body.data)).toBe(true);

      // Should find the document we just indexed
      if (searchResponse.body.data.length > 0) {
        const foundDoc = searchResponse.body.data.find(
          doc => doc.title === 'Integration Test Document',
        );
        expect(foundDoc).toBeDefined();
        expect(foundDoc.category).toBe('test');
      }
    });

    it('should handle multiple documents in the same index', async () => {
      const customerId = uuidv4();
      const index = 'multi-doc-test';

      // Index multiple documents
      const documents = [
        {
          title: 'Document 1',
          content: 'First document about technology and innovation',
          category: 'tech',
        },
        {
          title: 'Document 2',
          content: 'Second document about technology and development',
          category: 'tech',
        },
        {
          title: 'Document 3',
          content: 'Third document about cooking and recipes',
          category: 'food',
        },
      ];

      for (const doc of documents) {
        await request(app.getHttpServer())
          .post(`${baseUrl}/index`)
          .send({
            customerId,
            index: index,
            document: doc,
          })
          .expect(201);
      }

      // Wait for indexing
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Search for technology-related documents
      const searchResponse = await request(app.getHttpServer())
        .post(`${baseUrl}`)
        .send({
          index: index,
          size: 10,
          like: 'technology innovation development',
          fields: ['title', 'content'],
          filters: {
            category: 'tech',
          },
        })
        .expect(201);

      expect(searchResponse.body.data).toBeDefined();
      expect(Array.isArray(searchResponse.body.data)).toBe(true);

      // Should only return tech category documents
      searchResponse.body.data.forEach(doc => {
        expect(doc.category).toBe('tech');
      });
    });

    describe('Filter accuracy tests', () => {
      beforeEach(async () => {
        const customerId = 'filter-test-customer';
        const index = 'filter-accuracy-test';

        // Index documents with various filter combinations
        const testDocuments = [
          {
            customerId,
            index,
            document: {
              title: 'AI Research Paper',
              content: 'Advanced artificial intelligence research and machine learning algorithms',
              category: 'research',
              department: 'engineering',
              priority: 'high',
              status: 'published',
              author: 'Dr. Smith',
              tags: ['ai', 'ml', 'research'],
            },
          },
          {
            customerId,
            index,
            document: {
              title: 'Machine Learning Tutorial',
              content: 'Beginner guide to machine learning concepts and implementations',
              category: 'tutorial',
              department: 'engineering',
              priority: 'medium',
              status: 'draft',
              author: 'John Doe',
              tags: ['ml', 'tutorial', 'beginner'],
            },
          },
          {
            customerId,
            index,
            document: {
              title: 'Marketing Strategy',
              content: 'Comprehensive marketing strategy for product launch',
              category: 'business',
              department: 'marketing',
              priority: 'high',
              status: 'published',
              author: 'Jane Wilson',
              tags: ['marketing', 'strategy', 'business'],
            },
          },
          {
            customerId,
            index,
            document: {
              title: 'HR Policy Update',
              content: 'Updated human resources policies and procedures',
              category: 'policy',
              department: 'hr',
              priority: 'low',
              status: 'published',
              author: 'HR Team',
              tags: ['hr', 'policy', 'procedures'],
            },
          },
          {
            customerId,
            index,
            document: {
              title: 'Deep Learning Architecture',
              content: 'Neural network architectures for deep learning applications',
              category: 'research',
              department: 'engineering',
              priority: 'high',
              status: 'review',
              author: 'Dr. Smith',
              tags: ['deep-learning', 'neural-networks', 'ai'],
            },
          },
        ];

        // Index all test documents
        for (const doc of testDocuments) {
          await request(app.getHttpServer()).post(`${baseUrl}/index`).send(doc);
        }

        // Wait for indexing to complete
        await new Promise(resolve => setTimeout(resolve, 1500));
      });

      it('should filter by single field correctly', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'filter-accuracy-test',
            size: 10,
            like: 'machine learning artificial intelligence',
            fields: ['title', 'content'],
            filters: {
              department: 'engineering',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);

        // All results should have department: 'engineering'
        searchResponse.body.data.forEach(doc => {
          expect(doc.department).toBe('engineering');
        });
      });

      it('should filter by multiple fields correctly', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'filter-accuracy-test',
            size: 10,
            like: 'research and development',
            fields: ['title', 'content'],
            filters: {
              department: 'engineering',
              priority: 'high',
              category: 'research',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);

        // All results should match all filter criteria
        searchResponse.body.data.forEach(doc => {
          expect(doc.department).toBe('engineering');
          expect(doc.priority).toBe('high');
          expect(doc.category).toBe('research');
        });
      });

      it('should filter by author correctly', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'filter-accuracy-test',
            size: 10,
            like: 'artificial intelligence research',
            fields: ['title', 'content'],
            filters: {
              author: 'Dr. Smith',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);

        // All results should be authored by Dr. Smith
        searchResponse.body.data.forEach(doc => {
          expect(doc.author).toBe('Dr. Smith');
        });
      });

      it('should filter by status correctly', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'filter-accuracy-test',
            size: 10,
            like: 'documents and content',
            fields: ['title', 'content'],
            filters: {
              status: 'published',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);

        // All results should have status: 'published'
        searchResponse.body.data.forEach(doc => {
          expect(doc.status).toBe('published');
        });
      });

      it('should return empty results when filters match no documents', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'filter-accuracy-test',
            size: 10,
            like: 'any content',
            fields: ['title', 'content'],
            filters: {
              department: 'nonexistent-department',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);
        expect(searchResponse.body.data.length).toBe(0);
      });

      it('should handle complex filter combinations', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'filter-accuracy-test',
            size: 10,
            like: 'machine learning',
            fields: ['title', 'content'],
            filters: {
              department: 'engineering',
              status: 'draft',
              priority: 'medium',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);

        // Should find the ML tutorial document
        if (searchResponse.body.data.length > 0) {
          const foundDoc = searchResponse.body.data.find(
            doc => doc.title === 'Machine Learning Tutorial',
          );
          expect(foundDoc).toBeDefined();
          expect(foundDoc.department).toBe('engineering');
          expect(foundDoc.status).toBe('draft');
          expect(foundDoc.priority).toBe('medium');
        }
      });
    });

    describe('Search accuracy and relevance tests', () => {
      beforeEach(async () => {
        const customerId = 'accuracy-test-customer';
        const index = 'search-accuracy-test';

        // Index documents with varying relevance to test queries
        const testDocuments = [
          {
            index,
            document: {
              customerId,
              title: 'Machine Learning Fundamentals',
              content:
                'Comprehensive guide to machine learning algorithms, neural networks, and artificial intelligence concepts',
              category: 'education',
              relevanceScore: 'high',
              tags: ['machine-learning', 'ai', 'algorithms'],
            },
          },
          {
            index,
            document: {
              customerId,
              title: 'Introduction to AI',
              content:
                'Basic introduction to artificial intelligence and its applications in modern technology',
              category: 'education',
              relevanceScore: 'medium',
              tags: ['ai', 'introduction', 'technology'],
            },
          },
          {
            index,
            document: {
              customerId,
              title: 'Cooking with Machine',
              content: 'How to use kitchen machines and appliances for efficient cooking',
              category: 'lifestyle',
              relevanceScore: 'low',
              tags: ['cooking', 'kitchen', 'appliances'],
            },
          },
          {
            index,
            document: {
              customerId,
              title: 'Deep Learning Neural Networks',
              content:
                'Advanced deep learning techniques using convolutional and recurrent neural networks for AI applications',
              category: 'education',
              relevanceScore: 'high',
              tags: ['deep-learning', 'neural-networks', 'ai'],
            },
          },
          {
            index,
            document: {
              customerId,
              title: 'Learning Management System',
              content: 'Software system for managing educational courses and learning materials',
              category: 'software',
              relevanceScore: 'medium',
              tags: ['learning', 'management', 'education'],
            },
          },
        ];

        // Index all test documents
        for (const doc of testDocuments) {
          await request(app.getHttpServer()).post(`${baseUrl}/index`).send(doc);
        }

        // Wait for indexing to complete
        await new Promise(resolve => setTimeout(resolve, 1500));
      });

      it('should return results ordered by relevance score', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'search-accuracy-test',
            size: 5,
            like: 'machine learning artificial intelligence neural networks',
            fields: ['title', 'content', 'tags'],
            filters: {
              category: 'education',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);
        expect(searchResponse.body.data.length).toBeGreaterThan(0);

        // Results should have similarity scores
        searchResponse.body.data.forEach(doc => {
          expect(doc).toHaveProperty('score');
          expect(typeof doc.score).toBe('number');
        });

        // Results should be ordered by score (descending)
        for (let i = 1; i < searchResponse.body.data.length; i++) {
          expect(searchResponse.body.data[i - 1].score).toBeGreaterThanOrEqual(
            searchResponse.body.data[i].score,
          );
        }
      });

      it('should find highly relevant documents for specific queries', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'search-accuracy-test',
            size: 10,
            like: 'deep learning neural networks artificial intelligence',
            fields: ['title', 'content', 'tags'],
            filters: {
              category: 'education',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);

        // Should find the deep learning document with high relevance
        const deepLearningDoc = searchResponse.body.data.find(
          doc => doc.title === 'Deep Learning Neural Networks',
        );
        expect(deepLearningDoc).toBeDefined();
        expect(deepLearningDoc.relevanceScore).toBe('high');

        // Should also find machine learning fundamentals
        const mlDoc = searchResponse.body.data.find(
          doc => doc.title === 'Machine Learning Fundamentals',
        );
        expect(mlDoc).toBeDefined();
        expect(mlDoc.relevanceScore).toBe('high');
      });

      it('should exclude irrelevant documents', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'search-accuracy-test',
            size: 10,
            like: 'artificial intelligence machine learning algorithms',
            fields: ['title', 'content'],
            filters: {
              category: 'education',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);

        // Should not include the cooking document (different category and low relevance)
        const cookingDoc = searchResponse.body.data.find(
          doc => doc.title === 'Cooking with Machine',
        );
        expect(cookingDoc).toBeUndefined();
      });

      it('should handle partial matches correctly', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'search-accuracy-test',
            size: 10,
            like: 'machine learning and education systems',
            fields: ['title', 'content'],
            filters: {
              customerId: 'accuracy-test-customer',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);

        // Should find documents with "learning" in various contexts
        const titles = searchResponse.body.data.map(doc => doc.title);
        const hasLearningDocs = titles.some(
          title => title.includes('Learning') || title.includes('learning'),
        );
        expect(hasLearningDocs).toBe(true);
      });

      it('should search across multiple fields effectively', async () => {
        const searchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'search-accuracy-test',
            size: 10,
            like: 'neural networks deep learning',
            fields: ['title', 'content', 'tags'],
            filters: {
              customerId: 'accuracy-test-customer',
            },
          })
          .expect(201);

        expect(searchResponse.body.data).toBeDefined();
        expect(Array.isArray(searchResponse.body.data)).toBe(true);

        // Should find documents that match in title, content, or tags
        if (searchResponse.body.data.length > 0) {
          const hasRelevantMatch = searchResponse.body.data.some(doc => {
            const titleMatch =
              doc.title.toLowerCase().includes('neural') ||
              doc.title.toLowerCase().includes('deep');
            const contentMatch =
              doc.content.toLowerCase().includes('neural') ||
              doc.content.toLowerCase().includes('deep');
            const tagsMatch =
              doc.tags && doc.tags.some(tag => tag.includes('neural') || tag.includes('deep'));
            return titleMatch || contentMatch || tagsMatch;
          });
          expect(hasRelevantMatch).toBe(true);
        }
      });

      it('should handle size parameter correctly for result limiting', async () => {
        const smallSearchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'search-accuracy-test',
            size: 2,
            like: 'learning education',
            fields: ['title', 'content'],
            filters: {
              customerId: 'accuracy-test-customer',
            },
          })
          .expect(201);

        expect(smallSearchResponse.body.data).toBeDefined();
        expect(Array.isArray(smallSearchResponse.body.data)).toBe(true);
        expect(smallSearchResponse.body.data.length).toBeLessThanOrEqual(2);

        const largeSearchResponse = await request(app.getHttpServer())
          .post(`${baseUrl}`)
          .send({
            index: 'search-accuracy-test',
            size: 10,
            like: 'learning education',
            fields: ['title', 'content'],
            filters: {
              customerId: 'accuracy-test-customer',
            },
          })
          .expect(201);

        expect(largeSearchResponse.body.data).toBeDefined();
        expect(Array.isArray(largeSearchResponse.body.data)).toBe(true);
        expect(largeSearchResponse.body.data.length).toBeGreaterThanOrEqual(
          smallSearchResponse.body.data.length,
        );
      });
    });
  });
});
